export type StrapiResponsePagination = {
	page: number;
	pageCount: number;
	pageSize: number;
	total: number;
};

export type StrapiResponseMeta = {
	pagination: StrapiResponsePagination;
};

export type StrapiResponseData<T> = {
	data: T;
	meta: StrapiResponseMeta;
};

export type StrapiEntity<T, MetaData = any> = {
	// attributes: T & {
		createdAt?: string;
		metadata: MetaData;
		publishedAt?: string;
		seo?: StrapiSEO[];
		updatedAt?: string;
	// };
	id: number;
};

export type StrapiSEO = {
	keywords: Array<string>;
	metaDescription: string;
	metaImage: {
		data: { attributes: { url: string } };
	};
	metaTitle: string;
};

export type StrapiMediaFormat = {
	ext: string;
	hash: string;
	height: number;
	mime: string;
	name: string;
	path?: any;
	size: number;
	url: string;
	width: number;
};

export type StrapiMedia = StrapiEntity<{
	alternativeText?: any;
	caption?: any;
	createdAt?: string;
	ext: string;
	formats: {
		large?: StrapiMediaFormat;
		medium?: StrapiMediaFormat;
		small?: StrapiMediaFormat;
		thumbnail?: StrapiMediaFormat;
	};
	hash: string;
	height: number;
	mime: string;
	name: string;
	previewUrl?: any;
	provider: string;
	provider_metadata?: any;
	size: number;
	updatedAt?: string;
	url: string;
	width: number;
}>;
